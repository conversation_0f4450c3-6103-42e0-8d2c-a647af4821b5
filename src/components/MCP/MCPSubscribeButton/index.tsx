import {Button, ButtonProps, message} from '@panda-design/components';
import {MouseEvent, useCallback, CSSProperties, useEffect, useMemo} from 'react';
import {Popconfirm} from 'antd';
import {useBoolean} from 'huse';
import {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';
import {IconSubscribeBlue} from '@/icons/mcp';
import {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';
import {ApplicationBase} from '@/types/mcp/mcp';
import {IconSubscribeFilled} from '../../../icons/mcp/index';
import {useMCPWorkspaceId} from '../hooks';

interface Props {
    showText?: boolean;
    showCount?: boolean;
    id: number;
    workspaceId: number;
    enableGlobalParams?: boolean;
    refresh?: () => void;
    style?: CSSProperties;
    size?: ButtonProps['size'];
    iconColor?: string;
    className?: string;
}

export const MCPSubscribeButton = ({
    id,
    workspaceId,
    style,
    showText,
    showCount,
    size,
    enableGlobalParams,
    className,
}: Props) => {
    const [open, {on, off}] = useBoolean(false);
    const spaceId = useMCPWorkspaceId();
    const appList = useAppListForMCPServer(id);

    const apps = useMemo<ApplicationBase[]>(
        () => {
            const data = appList?.reduce((acc, cur) => {
                return [...acc, ...(spaceId && cur.workspaceId !== spaceId ? [] : cur.applications)];
            }, []);
            return data;
        },
        [appList, spaceId]
    );
    const subscribedNumber = useMemo(
        () => (apps ?? []).filter(app => app.ifSub).length,
        [apps]
    );
    const isSubscribed = subscribedNumber > 0;

    const handleClick = useCallback(
        async (e: MouseEvent) => {
            e.stopPropagation();
            try {
                if (apps?.length) {
                    setMCPSubscribe({
                        serverId: id,
                        workspaceId,
                        enableGlobalParams: enableGlobalParams ?? false,
                        onSuccess: () => loadAppListForMCPServer({mcpServerId: id}),
                    });
                }
                else {
                    on();
                }
            }
            catch (e) {
                message.error('服务器开小差了，请稍后重试');
            }
        },
        [apps, id, workspaceId, enableGlobalParams, on]
    );

    const handleCancel = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            off();
        },
        [off]
    );

    const handleOk = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            off();
            openMCPApplicationModal();
        },
        [off]
    );


    useEffect(
        () => {
            if (!spaceId && !apps?.length) {
                loadAppListForMCPServer({mcpServerId: id});
            }
            if (spaceId) {
                loadAppListForMCPServer({mcpServerId: id});
            }
        },
        [apps?.length, id, spaceId]
    );

    return (
        <Popconfirm
            title=""
            description="您还没有可用应用，请先创建后订阅"
            open={open}
            onConfirm={handleOk}
            onCancel={handleCancel}
            okText="立即创建"
            cancelText="稍后再说"
            placement="bottom"
        >
            <Button
                style={{padding: 0, gap: 3, ...style}}
                onClick={handleClick}
                className={className}
                icon={
                    isSubscribed
                        ? <IconSubscribeFilled style={{color: '#FFA400'}} />
                        : <IconSubscribeBlue />
                }
                type="text"
                size={size}
            >
                {showText
                    ? '订阅' + (showCount ? ` ${subscribedNumber}` : '')
                    : undefined
                }
            </Button>
        </Popconfirm>
    );
};

