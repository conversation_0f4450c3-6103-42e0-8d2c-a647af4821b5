import {Flex, Divider, Typography, Tooltip} from 'antd';
import {memo, MouseEvent, useMemo, useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {But<PERSON>} from '@panda-design/components';
import cx from 'classnames';
import {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MC<PERSON><PERSON> from '@/design/MCP/MCPCard';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import PublishInfo from '@/components/MCP/PublishInfo';
import SvgEye from '@/icons/mcp/Eye';
import {
    actionButtonHoverStyle,
    cardContentStyle,
    containerCss,
    DescriptionContainer,
    DescriptionText,
    dividerStyle,
    EllipsisOverlay,
    formatCount,
    hoverActionsStyle,
    iconStyle,
    protocolTextStyle,
    statsContainerStyle,
} from '@/components/MCP/BaseMCPCard/BaseMCPCard.styles';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}

const SpaceMCPCard = ({server, refresh}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();

    const handleClick = useCallback(
        () => {
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
        },
        [navigate, server.id]
    );

    const handleBasicInfoClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'basicInfo'}));
        },
        [navigate, spaceId, server.id]
    );

    const handleToolsConfigClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const tags = useMemo(
        () => (server.labels ?? []).map((label, index) => ({
            id: label.id || index,
            label: label.labelValue,
        })),
        [server.labels]
    );

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <Flex gap={14} align="center">
                <MCPServerAvatar icon={server.icon} />
                <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                    <Typography.Title level={4} ellipsis>
                        {server.name}
                    </Typography.Title>
                    <Flex align="center" gap={12}>
                        <Typography.Text style={protocolTextStyle}>
                            {getServerTypeText(server.serverSourceType)} | {server.serverProtocolType}
                        </Typography.Text>
                    </Flex>
                </Flex>
            </Flex>
            <Tooltip title={server.description || '暂无描述'} placement="top">
                <DescriptionContainer>
                    <DescriptionText>{server.description || '暂无描述'}</DescriptionText>
                    <EllipsisOverlay />
                </DescriptionContainer>
            </Tooltip>
            <TagGroup
                labels={tags}
                color="light-purple"
                prefix={null}
                style={{flexShrink: 1, overflow: 'hidden'}}
                gap={4}
            />
            <Divider style={dividerStyle} />
            <Flex justify="space-between" align="center">
                <Flex align="center" gap={12}>
                    <Tooltip title="浏览量">
                        <Flex
                            align="center"
                            gap={4}
                            onClick={handleViewCountClick}
                            className={statsContainerStyle}
                        >
                            <SvgEye style={iconStyle} />
                            {formatCount(server.viewCount)}
                        </Flex>
                    </Tooltip>
                </Flex>
                <PublishInfo
                    username={server.publishUser}
                    time={server.publishTime}
                />
            </Flex>
            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                <Button type="text" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>
                    基本信息
                </Button>
                <MCPSubscribeButton
                    refresh={refresh}
                    workspaceId={spaceId || server.workspaceId}
                    id={server.id}
                    className={cx(actionButtonHoverStyle)}
                    showText={false}
                    iconColor="#0083FF"
                />
                <Button type="text" onClick={handleToolsConfigClick} className={cx(actionButtonHoverStyle)}>
                    工具配置
                </Button>
            </Flex>
        </MCPCard>
    );
};

export default memo(SpaceMCPCard);
