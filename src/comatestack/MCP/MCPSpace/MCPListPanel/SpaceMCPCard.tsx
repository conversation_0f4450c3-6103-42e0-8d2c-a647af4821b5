import {memo, useCallback, MouseEvent} from 'react';
import {useNavigate} from 'react-router-dom';
import {MCPEditLink, MCPPlaygroundLink, MCPSpaceDetailLink} from '@/links/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import BaseMCPCard from '@/components/MCP/BaseMCPCard';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}

const SpaceMCPCard = ({server, refresh}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();

    const handleClick = useCallback(
        () => {
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
        },
        [navigate, server.id]
    );

    const handlePlaygroundClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            window.open(MCPPlaygroundLink.toUrl({serverId: server.id}), '_blank');
        },
        [server.id]
    );

    return (
        <BaseMCPCard
            server={server}
            refresh={refresh}
            showDepartment={false}
            workspaceId={spaceId}
            onCardClick={handleClick}
            onViewCountClick={handleViewCountClick}
            onPlaygroundClick={handlePlaygroundClick}
        />
    );
};

export default memo(SpaceMCPCard);
